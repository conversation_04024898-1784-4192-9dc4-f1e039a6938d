/**
 * Appraisal Council API services
 */

import { axiosClient, getAccessToken } from "../api";
import {
  AppraisalCouncilListRequest,
  AppraisalCouncilListResponse,
  CreateAppraisalCouncilRequest,
  UpdateAppraisalCouncilRequest,
} from "@/types/appraisal-council";

/**
 * Get list of appraisal councils with pagination and search
 */
export const getAppraisalCouncilList = async (
  request: AppraisalCouncilListRequest
): Promise<AppraisalCouncilListResponse> => {
  try {
    const accessToken = getAccessToken();
    if (!accessToken) {
      throw new Error("Access token not found");
    }

    const res = await axiosClient.post<AppraisalCouncilListResponse>(
      "/appraisal-council/list",
      request,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          "Content-Type": "application/json-patch+json",
        },
      }
    );

    return res.data;
  } catch (error) {
    console.error("getAppraisalCouncilList error:", error);
    throw error;
  }
};

/**
 * Create a new appraisal council
 */
export const createAppraisalCouncil = async (
  request: CreateAppraisalCouncilRequest
): Promise<string> => {
  try {
    const accessToken = getAccessToken();
    if (!accessToken) {
      throw new Error("Access token not found");
    }

    const res = await axiosClient.post<string>("/appraisal-council", request, {
      headers: {
        Authorization: `Bearer ${accessToken}`,
        "Content-Type": "application/json-patch+json",
      },
    });

    return res.data;
  } catch (error) {
    console.error("createAppraisalCouncil error:", error);
    throw error;
  }
};

/**
 * Update an existing appraisal council
 */
export const updateAppraisalCouncil = async (
  request: UpdateAppraisalCouncilRequest
): Promise<void> => {
  try {
    const accessToken = getAccessToken();
    if (!accessToken) {
      throw new Error("Access token not found");
    }

    await axiosClient.put("/appraisal-council", request, {
      headers: {
        Authorization: `Bearer ${accessToken}`,
        "Content-Type": "application/json-patch+json",
      },
    });
  } catch (error) {
    console.error("updateAppraisalCouncil error:", error);
    throw error;
  }
};

/**
 * Delete an appraisal council
 */
export const deleteAppraisalCouncil = async (
  councilId: string
): Promise<void> => {
  try {
    const accessToken = getAccessToken();
    if (!accessToken) {
      throw new Error("Access token not found");
    }

    await axiosClient.delete(`/appraisal-council/${councilId}`, {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
    });
  } catch (error) {
    console.error("deleteAppraisalCouncil error:", error);
    throw error;
  }
};
